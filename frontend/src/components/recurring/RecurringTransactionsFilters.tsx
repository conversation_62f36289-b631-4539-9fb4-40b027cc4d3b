import { useQuery } from '@tanstack/react-query'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { X } from 'lucide-react'
import { accountsApi } from '@/lib/api'
import { useCategoriesForForms } from '@/hooks/useCategories'
import { 
  TRANSACTION_TYPE_LABELS, 
  RECURRENCE_FREQUENCY_LABELS,
  TRANSACTION_TYPES,
  RECURRENCE_FREQUENCIES
} from '@/types/transaction.types'

interface RecurringTransactionsFiltersProps {
  filters: {
    type: string
    frequency: string
    accountId: string
    categoryId: string
    isActive: string
  }
  onFiltersChange: (filters: any) => void
}

export function RecurringTransactionsFilters({ 
  filters, 
  onFiltersChange 
}: RecurringTransactionsFiltersProps) {
  const { data: accounts } = useQuery({
    queryKey: ['accounts'],
    queryFn: () => accountsApi.getAll(),
    select: (data) => data.data || []
  })

  const { data: categories } = useCategoriesForForms()

  const handleFilterChange = (key: string, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const clearFilters = () => {
    onFiltersChange({
      type: '',
      frequency: '',
      accountId: '',
      categoryId: '',
      isActive: '',
    })
  }

  const hasActiveFilters = Object.values(filters).some(value => value !== '')

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        {/* Transaction Type Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">
            Tipo
          </label>
          <Select
            value={filters.type}
            onValueChange={(value) => handleFilterChange('type', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Todos os tipos" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Todos os tipos</SelectItem>
              {Object.entries(TRANSACTION_TYPE_LABELS).map(([key, label]) => (
                <SelectItem key={key} value={key}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Frequency Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">
            Frequência
          </label>
          <Select
            value={filters.frequency}
            onValueChange={(value) => handleFilterChange('frequency', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Todas as frequências" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Todas as frequências</SelectItem>
              {Object.entries(RECURRENCE_FREQUENCY_LABELS).map(([key, label]) => (
                <SelectItem key={key} value={key}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Account Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">
            Conta
          </label>
          <Select
            value={filters.accountId}
            onValueChange={(value) => handleFilterChange('accountId', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Todas as contas" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Todas as contas</SelectItem>
              {accounts?.map((account) => (
                <SelectItem key={account.id} value={account.id}>
                  {account.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Category Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">
            Categoria
          </label>
          <Select
            value={filters.categoryId}
            onValueChange={(value) => handleFilterChange('categoryId', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Todas as categorias" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Todas as categorias</SelectItem>
              {categories?.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Status Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">
            Status
          </label>
          <Select
            value={filters.isActive}
            onValueChange={(value) => handleFilterChange('isActive', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Todos os status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Todos os status</SelectItem>
              <SelectItem value="true">Ativas</SelectItem>
              <SelectItem value="false">Inativas</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Clear Filters Button */}
      {hasActiveFilters && (
        <div className="flex justify-end">
          <Button
            variant="outline"
            size="sm"
            onClick={clearFilters}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Limpar Filtros
          </Button>
        </div>
      )}
    </div>
  )
}
